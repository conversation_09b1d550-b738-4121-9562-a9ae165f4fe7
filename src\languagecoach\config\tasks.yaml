# ============================================================================
# JAPANESE LANGUAGE ASSESSMENT TASKS
# ============================================================================

generate_assessment_questions:
  description: >
    Generate a comprehensive set of assessment questions to evaluate a student's Japanese language proficiency.
    Create questions across different skill areas: vocabulary, grammar, kanji recognition, reading comprehension,
    and listening comprehension. Ensure questions span JLPT levels N5 through N1 to accurately determine the
    student's current level. Include cultural context questions and practical usage scenarios.
    Student's native language: {native_language}
    Target assessment areas: {assessment_areas}
  expected_output: >
    A structured assessment with 25-30 questions covering:
    - 5-6 vocabulary questions (multiple choice and translation)
    - 5-6 grammar questions (sentence completion and usage)
    - 4-5 kanji recognition questions
    - 4-5 reading comprehension questions
    - 3-4 cultural context questions
    Each question should include: question text, options (if multiple choice), correct answer,
    JLPT level, topic area, and explanation. Format as JSON structure.
  agent: japanese_assessor

evaluate_assessment_responses:
  description: >
    Analyze the student's responses to the assessment questions and determine their current Japanese proficiency level.
    Evaluate each answer for correctness, identify patterns in strengths and weaknesses, and provide detailed
    feedback. Calculate scores for different skill areas and determine the appropriate JLPT level.
    Student responses: {student_responses}
    Assessment questions: {assessment_questions}
  expected_output: >
    A comprehensive assessment result including:
    - Determined JLPT level (N5, N4, N3, N2, or N1)
    - Overall score percentage
    - Detailed breakdown by skill area (vocabulary, grammar, kanji, reading, culture)
    - List of identified strengths (minimum 3)
    - List of areas for improvement (minimum 3)
    - Specific recommendations for learning focus
    - Suggested study approach based on learning style
    Format as structured JSON with clear explanations.
  agent: japanese_assessor

# ============================================================================
# CURRICULUM DESIGN TASKS
# ============================================================================

create_learning_roadmap:
  description: >
    Design a personalized learning roadmap for the student based on their assessment results and learning goals.
    Create a structured progression from their current level to their target level, incorporating their available
    study time and specific learning objectives. Include milestone checkpoints and adaptive elements.
    Current level: {current_level}
    Target level: {target_level}
    Learning goals: {learning_goals}
    Study time per week: {study_hours_per_week}
    Assessment results: {assessment_results}
  expected_output: >
    A detailed learning roadmap including:
    - Estimated timeline to reach target level
    - Structured lesson sequence with 36-48 lessons
    - Weekly study schedule recommendations
    - Learning milestones and checkpoint assessments
    - Skill-specific focus areas based on assessment weaknesses
    - Cultural immersion activities
    - JLPT preparation timeline (if applicable)
    - Adaptive elements for different learning paces
    Format as structured JSON with clear progression logic.
  agent: curriculum_designer

# ============================================================================
# LESSON CREATION TASKS
# ============================================================================

create_lesson_content:
  description: >
    Create comprehensive lesson content for a specific Japanese language topic and proficiency level.
    Include vocabulary, grammar explanations, cultural context, practice exercises, and real-world applications.
    Ensure content is engaging, pedagogically sound, and appropriate for the target level.
    Lesson topic: {lesson_topic}
    JLPT level: {jlpt_level}
    Lesson type: {lesson_type}
    Student's current progress: {student_progress}
  expected_output: >
    Complete lesson content including:
    - Engaging lesson introduction with learning objectives
    - 8-12 new vocabulary items with readings, meanings, and example sentences
    - 2-3 grammar points with clear explanations and usage examples
    - Cultural notes relevant to the lesson topic
    - 5-8 practice exercises (multiple choice, fill-in-blank, translation)
    - Real-world application scenarios
    - Lesson summary and key takeaways
    - Homework or additional practice suggestions
    Format as structured JSON with clear organization.
  agent: lesson_creator

# ============================================================================
# PROGRESS TRACKING TASKS
# ============================================================================

analyze_student_progress:
  description: >
    Analyze the student's learning progress, performance patterns, and engagement metrics to provide
    insights and recommendations for optimizing their learning experience. Identify areas of strength,
    weakness, and potential learning obstacles.
    Student ID: {student_id}
    Progress data: {progress_data}
    Recent performance: {recent_performance}
    Time period: {analysis_period}
  expected_output: >
    Comprehensive progress analysis including:
    - Overall progress percentage toward target level
    - Performance trends over time
    - Strengths and areas needing improvement
    - Learning velocity and consistency metrics
    - Identification of difficult concepts or patterns
    - Recommendations for study approach adjustments
    - Suggested review topics or additional practice
    - Motivation and engagement insights
    Format as structured JSON with actionable recommendations.
  agent: progress_tracker

# ============================================================================
# CONVERSATION PRACTICE TASKS
# ============================================================================

facilitate_conversation_practice:
  description: >
    Engage the student in natural Japanese conversation practice appropriate for their level.
    Provide a supportive environment for speaking practice, gentle corrections, and cultural guidance.
    Adapt conversation topics and complexity based on the student's proficiency and interests.
    Student level: {student_level}
    Conversation topic: {conversation_topic}
    Practice focus: {practice_focus}
    Student interests: {student_interests}
  expected_output: >
    Interactive conversation session including:
    - Natural conversation flow with appropriate level vocabulary and grammar
    - Gentle corrections with explanations when needed
    - Cultural context and social cues guidance
    - Encouragement and positive feedback
    - Suggestions for improvement
    - Follow-up practice recommendations
    - New vocabulary or expressions introduced naturally
    Format as conversational dialogue with embedded teaching moments.
  agent: conversation_partner

# ============================================================================
# LEGACY TASKS (for backward compatibility)
# ============================================================================

research_task:
  description: >
    Conduct a thorough research about {topic}
    Make sure you find any interesting and relevant information given
    the current year is {current_year}.
  expected_output: >
    A list with 10 bullet points of the most relevant information about {topic}
  agent: researcher

reporting_task:
  description: >
    Review the context you got and expand each topic into a full section for a report.
    Make sure the report is detailed and contains any and all relevant information.
  expected_output: >
    A fully fledged report with the main topics, each with a full section of information.
    Formatted as markdown without '```'
  agent: reporting_analyst
