# CrewAI FastAPI Backend

A proof of concept FastAPI backend that integrates with CrewAI agents for research and reporting tasks.

## Features

- **RESTful API** for CrewAI agent execution
- **Asynchronous job processing** with status tracking
- **Automatic API documentation** with Swagger/OpenAPI
- **CORS support** for frontend integration
- **Job management** with cleanup capabilities
- **Error handling** and logging

## API Endpoints

### Health Check
- `GET /api/v1/health` - Check API health status

### Crew Operations
- `POST /api/v1/crew/run` - Start a new crew execution
- `GET /api/v1/crew/status/{job_id}` - Get job execution status
- `GET /api/v1/crew/result/{job_id}` - Get job execution results
- `GET /api/v1/crew/jobs` - List all jobs
- `DELETE /api/v1/crew/jobs/cleanup` - Clean up old completed jobs

### Documentation
- `GET /docs` - Interactive API documentation (Swagger UI)
- `GET /redoc` - Alternative API documentation (ReDoc)

## Quick Start

### 1. Install Dependencies

```bash
cd languagecoach
pip install fastapi uvicorn[standard] python-multipart requests
```

### 2. Set Environment Variables

Create a `.env` file in the languagecoach directory:

```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Start the Server

```bash
# From the languagecoach/src directory
cd src
python -m languagecoach.api.main

# Or using the script (from languagecoach directory)
python -m languagecoach.api.main
```

The server will start on `http://127.0.0.1:8000`

### 4. Test the API

```bash
# Test the health endpoint
curl -X GET "http://127.0.0.1:8000/api/v1/health"

# Start a crew job
curl -X POST "http://127.0.0.1:8000/api/v1/crew/run" \
  -H "Content-Type: application/json" \
  -d '{"topic": "Artificial Intelligence", "current_year": "2024"}'

# Or run the test script
python test_api.py
```

## API Usage Examples

### Starting a Crew Job

```python
import requests

response = requests.post("http://127.0.0.1:8000/api/v1/crew/run", json={
    "topic": "Machine Learning Trends",
    "current_year": "2024"
})

job_data = response.json()
job_id = job_data["job_id"]
print(f"Job started: {job_id}")
```

### Checking Job Status

```python
import requests
import time

job_id = "your-job-id-here"

while True:
    response = requests.get(f"http://127.0.0.1:8000/api/v1/crew/status/{job_id}")
    status_data = response.json()
    
    print(f"Status: {status_data['status']}")
    print(f"Progress: {status_data.get('progress', 'N/A')}")
    
    if status_data['status'] in ['completed', 'failed']:
        break
    
    time.sleep(5)  # Wait 5 seconds before checking again
```

### Getting Job Results

```python
import requests

job_id = "your-job-id-here"
response = requests.get(f"http://127.0.0.1:8000/api/v1/crew/result/{job_id}")

if response.status_code == 200:
    result_data = response.json()
    if result_data['status'] == 'completed':
        print("Job completed successfully!")
        print("Results:", result_data['result'])
    else:
        print(f"Job status: {result_data['status']}")
        if result_data.get('error'):
            print(f"Error: {result_data['error']}")
```

## Configuration

### Environment Variables

- `OPENAI_API_KEY` - Required for CrewAI agents
- `API_HOST` - Server host (default: 127.0.0.1)
- `API_PORT` - Server port (default: 8000)
- `API_RELOAD` - Enable auto-reload in development (default: true)

### Crew Configuration

The crew uses the existing configuration files:
- `src/languagecoach/config/agents.yaml` - Agent definitions
- `src/languagecoach/config/tasks.yaml` - Task definitions

## Architecture

```
languagecoach/
├── src/languagecoach/
│   ├── api/
│   │   ├── __init__.py
│   │   ├── main.py          # FastAPI application
│   │   ├── models.py        # Pydantic models
│   │   ├── crew_manager.py  # Job management
│   │   └── routes.py        # API endpoints
│   ├── config/              # CrewAI configuration
│   ├── crew.py              # CrewAI crew definition
│   └── main.py              # CLI interface
├── test_api.py              # API test script
└── .env                     # Environment variables
```

## Job Lifecycle

1. **Submit Job** - POST to `/crew/run` with topic and parameters
2. **Job Queued** - Job gets unique ID and enters "pending" status
3. **Job Running** - Crew execution starts, status becomes "running"
4. **Job Complete** - Status becomes "completed" or "failed"
5. **Get Results** - Retrieve results via `/crew/result/{job_id}`

## Error Handling

The API provides structured error responses:

```json
{
  "error": "ValidationError",
  "message": "Invalid input parameters",
  "details": {"field": "topic", "issue": "Field is required"}
}
```

Common HTTP status codes:
- `200` - Success
- `202` - Accepted (job still running)
- `400` - Bad Request (invalid input)
- `404` - Not Found (job not found)
- `500` - Internal Server Error

## Development

### Running in Development Mode

The server runs with auto-reload enabled by default in development.

### Adding New Endpoints

1. Add route functions to `routes.py`
2. Define request/response models in `models.py`
3. Update documentation as needed

### Testing

Use the provided `test_api.py` script or the interactive documentation at `/docs`.

## Production Considerations

- Set proper CORS origins instead of allowing all (`*`)
- Use environment-specific configuration
- Implement proper authentication if needed
- Add rate limiting
- Use a production ASGI server like Gunicorn
- Implement persistent job storage (database)
- Add monitoring and logging
