"""
Pydantic models for the Japanese Language Coach FastAPI backend.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class JLPTLevel(str, Enum):
    """JLPT (Japanese Language Proficiency Test) levels."""
    N5 = "N5"  # Beginner
    N4 = "N4"  # Elementary
    N3 = "N3"  # Intermediate
    N2 = "N2"  # Upper Intermediate
    N1 = "N1"  # Advanced


class LessonType(str, Enum):
    """Types of Japanese language lessons."""
    VOCABULARY = "vocabulary"
    GRAMMAR = "grammar"
    KANJI = "kanji"
    READING = "reading"
    LISTENING = "listening"
    CONVERSATION = "conversation"
    CULTURE = "culture"


class AssessmentQuestionType(str, Enum):
    """Types of assessment questions."""
    MULTIPLE_CHOICE = "multiple_choice"
    TRANSLATION = "translation"
    READING_COMPREHENSION = "reading_comprehension"
    LISTENING_COMPREHENSION = "listening_comprehension"
    KANJI_RECOGNITION = "kanji_recognition"
    GRAMMAR_COMPLETION = "grammar_completion"


class LearningGoal(str, Enum):
    """Student learning goals."""
    JLPT_PREPARATION = "jlpt_preparation"
    BUSINESS_JAPANESE = "business_japanese"
    TRAVEL_JAPANESE = "travel_japanese"
    ACADEMIC_JAPANESE = "academic_japanese"
    GENERAL_FLUENCY = "general_fluency"
    ANIME_MANGA = "anime_manga"


# ============================================================================
# STUDENT PROFILE MODELS
# ============================================================================

class StudentProfile(BaseModel):
    """Student profile information."""
    student_id: str = Field(..., description="Unique student identifier")
    name: str = Field(..., description="Student's name")
    email: Optional[str] = Field(None, description="Student's email address")
    current_level: Optional[JLPTLevel] = Field(None, description="Current JLPT level")
    target_level: Optional[JLPTLevel] = Field(None, description="Target JLPT level")
    learning_goals: List[LearningGoal] = Field(default=[], description="Student's learning goals")
    native_language: str = Field(default="English", description="Student's native language")
    study_time_per_week: Optional[int] = Field(None, description="Available study hours per week")
    created_at: datetime = Field(default_factory=datetime.now, description="Profile creation date")

    class Config:
        json_schema_extra = {
            "example": {
                "student_id": "student_123",
                "name": "John Doe",
                "email": "<EMAIL>",
                "current_level": "N5",
                "target_level": "N3",
                "learning_goals": ["jlpt_preparation", "travel_japanese"],
                "native_language": "English",
                "study_time_per_week": 10
            }
        }


class CreateStudentRequest(BaseModel):
    """Request to create a new student profile."""
    name: str = Field(..., description="Student's name")
    email: Optional[str] = Field(None, description="Student's email address")
    target_level: Optional[JLPTLevel] = Field(None, description="Target JLPT level")
    learning_goals: List[LearningGoal] = Field(default=[], description="Student's learning goals")
    native_language: str = Field(default="English", description="Student's native language")
    study_time_per_week: Optional[int] = Field(None, description="Available study hours per week")


# ============================================================================
# ASSESSMENT MODELS
# ============================================================================

class AssessmentQuestion(BaseModel):
    """A single assessment question."""
    question_id: str = Field(..., description="Unique question identifier")
    question_type: AssessmentQuestionType = Field(..., description="Type of question")
    question_text: str = Field(..., description="The question text")
    options: Optional[List[str]] = Field(None, description="Multiple choice options")
    correct_answer: str = Field(..., description="Correct answer")
    level: JLPTLevel = Field(..., description="JLPT level this question tests")
    topic: str = Field(..., description="Topic or skill being tested")
    points: int = Field(default=1, description="Points awarded for correct answer")


class AssessmentAnswer(BaseModel):
    """Student's answer to an assessment question."""
    question_id: str = Field(..., description="Question identifier")
    student_answer: str = Field(..., description="Student's answer")
    is_correct: Optional[bool] = Field(None, description="Whether answer is correct")
    points_earned: Optional[int] = Field(None, description="Points earned")


class StartAssessmentRequest(BaseModel):
    """Request to start a new assessment."""
    student_id: str = Field(..., description="Student identifier")
    assessment_type: str = Field(default="level_placement", description="Type of assessment")


class SubmitAnswerRequest(BaseModel):
    """Request to submit an answer to an assessment question."""
    assessment_id: str = Field(..., description="Assessment session identifier")
    question_id: str = Field(..., description="Question identifier")
    answer: str = Field(..., description="Student's answer")


class AssessmentResult(BaseModel):
    """Results of a completed assessment."""
    assessment_id: str = Field(..., description="Assessment identifier")
    student_id: str = Field(..., description="Student identifier")
    determined_level: JLPTLevel = Field(..., description="Determined JLPT level")
    total_questions: int = Field(..., description="Total number of questions")
    correct_answers: int = Field(..., description="Number of correct answers")
    score_percentage: float = Field(..., description="Overall score percentage")
    level_scores: Dict[str, float] = Field(..., description="Scores by JLPT level")
    strengths: List[str] = Field(default=[], description="Identified strengths")
    weaknesses: List[str] = Field(default=[], description="Areas for improvement")
    recommendations: List[str] = Field(default=[], description="Learning recommendations")
    completed_at: datetime = Field(default_factory=datetime.now, description="Completion timestamp")


# ============================================================================
# LEARNING ROADMAP MODELS
# ============================================================================

class LessonPlan(BaseModel):
    """Individual lesson in a learning roadmap."""
    lesson_id: str = Field(..., description="Unique lesson identifier")
    title: str = Field(..., description="Lesson title")
    description: str = Field(..., description="Lesson description")
    level: JLPTLevel = Field(..., description="JLPT level")
    lesson_type: LessonType = Field(..., description="Type of lesson")
    estimated_duration: int = Field(..., description="Estimated duration in minutes")
    prerequisites: List[str] = Field(default=[], description="Required previous lessons")
    learning_objectives: List[str] = Field(default=[], description="Learning objectives")
    vocabulary_count: Optional[int] = Field(None, description="Number of vocabulary items")
    grammar_points: List[str] = Field(default=[], description="Grammar points covered")


class LearningRoadmap(BaseModel):
    """Personalized learning roadmap for a student."""
    roadmap_id: str = Field(..., description="Unique roadmap identifier")
    student_id: str = Field(..., description="Student identifier")
    current_level: JLPTLevel = Field(..., description="Student's current level")
    target_level: JLPTLevel = Field(..., description="Target level")
    estimated_duration_weeks: int = Field(..., description="Estimated completion time in weeks")
    lessons: List[LessonPlan] = Field(..., description="Ordered list of lessons")
    milestones: List[str] = Field(default=[], description="Learning milestones")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")


class CreateRoadmapRequest(BaseModel):
    """Request to create a learning roadmap."""
    student_id: str = Field(..., description="Student identifier")
    assessment_id: str = Field(..., description="Assessment results to base roadmap on")
    target_level: Optional[JLPTLevel] = Field(None, description="Override target level")
    study_hours_per_week: Optional[int] = Field(None, description="Available study time")


# ============================================================================
# LESSON CONTENT MODELS
# ============================================================================

class VocabularyItem(BaseModel):
    """A vocabulary item in a lesson."""
    word: str = Field(..., description="Japanese word")
    reading: str = Field(..., description="Hiragana/katakana reading")
    meaning: str = Field(..., description="English meaning")
    example_sentence: Optional[str] = Field(None, description="Example sentence")
    example_translation: Optional[str] = Field(None, description="Example translation")
    level: JLPTLevel = Field(..., description="JLPT level")


class GrammarPoint(BaseModel):
    """A grammar point in a lesson."""
    pattern: str = Field(..., description="Grammar pattern")
    explanation: str = Field(..., description="Grammar explanation")
    examples: List[str] = Field(default=[], description="Example sentences")
    translations: List[str] = Field(default=[], description="Example translations")
    level: JLPTLevel = Field(..., description="JLPT level")


class Exercise(BaseModel):
    """An exercise in a lesson."""
    exercise_id: str = Field(..., description="Exercise identifier")
    exercise_type: str = Field(..., description="Type of exercise")
    question: str = Field(..., description="Exercise question")
    options: Optional[List[str]] = Field(None, description="Multiple choice options")
    correct_answer: str = Field(..., description="Correct answer")
    explanation: Optional[str] = Field(None, description="Answer explanation")


class LessonContent(BaseModel):
    """Complete lesson content."""
    lesson_id: str = Field(..., description="Lesson identifier")
    title: str = Field(..., description="Lesson title")
    level: JLPTLevel = Field(..., description="JLPT level")
    lesson_type: LessonType = Field(..., description="Type of lesson")
    introduction: str = Field(..., description="Lesson introduction")
    vocabulary: List[VocabularyItem] = Field(default=[], description="Vocabulary items")
    grammar: List[GrammarPoint] = Field(default=[], description="Grammar points")
    exercises: List[Exercise] = Field(default=[], description="Practice exercises")
    cultural_notes: Optional[str] = Field(None, description="Cultural context")
    summary: str = Field(..., description="Lesson summary")


class StartLessonRequest(BaseModel):
    """Request to start a lesson."""
    student_id: str = Field(..., description="Student identifier")
    lesson_id: str = Field(..., description="Lesson identifier")


# ============================================================================
# PROGRESS TRACKING MODELS
# ============================================================================

class LessonProgress(BaseModel):
    """Progress on a specific lesson."""
    lesson_id: str = Field(..., description="Lesson identifier")
    student_id: str = Field(..., description="Student identifier")
    status: str = Field(..., description="Completion status")
    score: Optional[float] = Field(None, description="Lesson score (0-100)")
    time_spent_minutes: Optional[int] = Field(None, description="Time spent on lesson")
    exercises_completed: int = Field(default=0, description="Number of exercises completed")
    exercises_correct: int = Field(default=0, description="Number of correct exercises")
    started_at: Optional[datetime] = Field(None, description="When lesson was started")
    completed_at: Optional[datetime] = Field(None, description="When lesson was completed")
    mistakes: List[str] = Field(default=[], description="Common mistakes made")


class ProgressUpdate(BaseModel):
    """Update student progress."""
    student_id: str = Field(..., description="Student identifier")
    lesson_id: str = Field(..., description="Lesson identifier")
    exercise_id: Optional[str] = Field(None, description="Exercise identifier")
    action: str = Field(..., description="Action taken (start, complete, answer)")
    score: Optional[float] = Field(None, description="Score achieved")
    time_spent: Optional[int] = Field(None, description="Time spent in minutes")
    answer_data: Optional[Dict[str, Any]] = Field(None, description="Answer details")


class StudentProgressReport(BaseModel):
    """Comprehensive progress report for a student."""
    student_id: str = Field(..., description="Student identifier")
    current_level: JLPTLevel = Field(..., description="Current assessed level")
    lessons_completed: int = Field(..., description="Number of lessons completed")
    total_lessons: int = Field(..., description="Total lessons in roadmap")
    overall_score: float = Field(..., description="Overall score percentage")
    study_streak_days: int = Field(..., description="Current study streak")
    total_study_time_hours: float = Field(..., description="Total study time")
    strengths: List[str] = Field(default=[], description="Current strengths")
    areas_for_improvement: List[str] = Field(default=[], description="Areas needing work")
    next_lesson: Optional[str] = Field(None, description="Next recommended lesson")
    generated_at: datetime = Field(default_factory=datetime.now, description="Report generation time")


# ============================================================================
# LEGACY MODELS (for backward compatibility)
# ============================================================================

class CrewRunRequest(BaseModel):
    """Request model for running a crew."""
    topic: str = Field(..., description="The topic for the crew to research and analyze")
    current_year: Optional[str] = Field(None, description="Current year for context (defaults to current year)")

    class Config:
        json_schema_extra = {
            "example": {
                "topic": "AI LLMs",
                "current_year": "2024"
            }
        }


class CrewRunResponse(BaseModel):
    """Response model for crew run initiation."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current status of the job")
    message: str = Field(..., description="Human-readable message about the job")
    created_at: datetime = Field(..., description="When the job was created")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "pending",
                "message": "Crew execution started successfully",
                "created_at": "2024-01-01T12:00:00Z"
            }
        }


class JobStatusResponse(BaseModel):
    """Response model for job status queries."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current status of the job")
    created_at: datetime = Field(..., description="When the job was created")
    started_at: Optional[datetime] = Field(None, description="When the job execution started")
    completed_at: Optional[datetime] = Field(None, description="When the job was completed")
    progress: Optional[str] = Field(None, description="Current progress information")
    error: Optional[str] = Field(None, description="Error message if job failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "running",
                "created_at": "2024-01-01T12:00:00Z",
                "started_at": "2024-01-01T12:00:05Z",
                "completed_at": None,
                "progress": "Researcher agent is analyzing the topic",
                "error": None
            }
        }


class JobResultResponse(BaseModel):
    """Response model for job results."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Final status of the job")
    created_at: datetime = Field(..., description="When the job was created")
    completed_at: Optional[datetime] = Field(None, description="When the job was completed")
    result: Optional[Dict[str, Any]] = Field(None, description="The crew execution result")
    error: Optional[str] = Field(None, description="Error message if job failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "completed",
                "created_at": "2024-01-01T12:00:00Z",
                "completed_at": "2024-01-01T12:05:30Z",
                "result": {
                    "research_findings": "...",
                    "final_report": "..."
                },
                "error": None
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model."""
    error: str = Field(..., description="Error type or code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid input parameters",
                "details": {"field": "topic", "issue": "Field is required"}
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Service health status")
    timestamp: datetime = Field(..., description="Current timestamp")
    version: str = Field(..., description="API version")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2024-01-01T12:00:00Z",
                "version": "0.1.0"
            }
        }
