"""
Crew execution manager for handling CrewAI job lifecycle.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from concurrent.futures import ThreadPoolExecutor

from ..crew import Languagecoach
from .models import JobStatus, JobStatusResponse, JobResultResponse


logger = logging.getLogger(__name__)


class JobInfo:
    """Information about a crew execution job."""
    
    def __init__(self, job_id: str, inputs: Dict[str, Any]):
        self.job_id = job_id
        self.inputs = inputs
        self.status = JobStatus.PENDING
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Optional[Dict[str, Any]] = None
        self.error: Optional[str] = None
        self.progress: Optional[str] = None


class CrewManager:
    """Manages CrewAI job execution and tracking."""
    
    def __init__(self):
        self.jobs: Dict[str, JobInfo] = {}
        self.executor = ThreadPoolExecutor(max_workers=3)  # Limit concurrent crew executions
        
    async def start_crew_job(self, inputs: Dict[str, Any]) -> str:
        """Start a new crew execution job."""
        job_id = str(uuid.uuid4())
        job_info = JobInfo(job_id, inputs)
        self.jobs[job_id] = job_info
        
        # Start the crew execution in background
        asyncio.create_task(self._execute_crew(job_id))
        
        logger.info(f"Started crew job {job_id} with inputs: {inputs}")
        return job_id
    
    async def _execute_crew(self, job_id: str) -> None:
        """Execute the crew in a background task."""
        job_info = self.jobs.get(job_id)
        if not job_info:
            logger.error(f"Job {job_id} not found")
            return
            
        try:
            job_info.status = JobStatus.RUNNING
            job_info.started_at = datetime.now()
            job_info.progress = "Initializing crew execution"
            
            logger.info(f"Executing crew for job {job_id}")
            
            # Run the crew execution in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor, 
                self._run_crew_sync, 
                job_info.inputs
            )
            
            job_info.result = result
            job_info.status = JobStatus.COMPLETED
            job_info.completed_at = datetime.now()
            job_info.progress = "Crew execution completed successfully"
            
            logger.info(f"Crew job {job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Crew job {job_id} failed: {str(e)}")
            job_info.status = JobStatus.FAILED
            job_info.completed_at = datetime.now()
            job_info.error = str(e)
            job_info.progress = f"Crew execution failed: {str(e)}"
    
    def _run_crew_sync(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Run the crew synchronously (called from thread pool)."""
        try:
            # Create crew instance
            crew_instance = Languagecoach()
            crew = crew_instance.crew()
            
            # Execute the crew
            result = crew.kickoff(inputs=inputs)
            
            # Convert result to dictionary format
            if hasattr(result, 'raw'):
                # If result has raw attribute, use it
                return {"raw_output": result.raw, "result": str(result)}
            else:
                # Otherwise convert to string
                return {"result": str(result)}
                
        except Exception as e:
            logger.error(f"Error in crew execution: {str(e)}")
            raise
    
    def get_job_status(self, job_id: str) -> Optional[JobStatusResponse]:
        """Get the status of a job."""
        job_info = self.jobs.get(job_id)
        if not job_info:
            return None
            
        return JobStatusResponse(
            job_id=job_info.job_id,
            status=job_info.status,
            created_at=job_info.created_at,
            started_at=job_info.started_at,
            completed_at=job_info.completed_at,
            progress=job_info.progress,
            error=job_info.error
        )
    
    def get_job_result(self, job_id: str) -> Optional[JobResultResponse]:
        """Get the result of a completed job."""
        job_info = self.jobs.get(job_id)
        if not job_info:
            return None
            
        return JobResultResponse(
            job_id=job_info.job_id,
            status=job_info.status,
            created_at=job_info.created_at,
            completed_at=job_info.completed_at,
            result=job_info.result,
            error=job_info.error
        )
    
    def list_jobs(self) -> Dict[str, JobStatusResponse]:
        """List all jobs and their statuses."""
        return {
            job_id: JobStatusResponse(
                job_id=job_info.job_id,
                status=job_info.status,
                created_at=job_info.created_at,
                started_at=job_info.started_at,
                completed_at=job_info.completed_at,
                progress=job_info.progress,
                error=job_info.error
            )
            for job_id, job_info in self.jobs.items()
        }
    
    def cleanup_old_jobs(self, max_age_hours: int = 24) -> int:
        """Clean up old completed jobs."""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        jobs_to_remove = []
        
        for job_id, job_info in self.jobs.items():
            if (job_info.status in [JobStatus.COMPLETED, JobStatus.FAILED] and 
                job_info.created_at.timestamp() < cutoff_time):
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.jobs[job_id]
            
        logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
        return len(jobs_to_remove)


# Global crew manager instance
crew_manager = CrewManager()
