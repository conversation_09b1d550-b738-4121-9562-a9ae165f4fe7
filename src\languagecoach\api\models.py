"""
Pydantic models for the FastAPI CrewAI backend.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class CrewRunRequest(BaseModel):
    """Request model for running a crew."""
    topic: str = Field(..., description="The topic for the crew to research and analyze")
    current_year: Optional[str] = Field(None, description="Current year for context (defaults to current year)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "topic": "AI LLMs",
                "current_year": "2024"
            }
        }


class CrewRunResponse(BaseModel):
    """Response model for crew run initiation."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current status of the job")
    message: str = Field(..., description="Human-readable message about the job")
    created_at: datetime = Field(..., description="When the job was created")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "pending",
                "message": "Crew execution started successfully",
                "created_at": "2024-01-01T12:00:00Z"
            }
        }


class JobStatusResponse(BaseModel):
    """Response model for job status queries."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Current status of the job")
    created_at: datetime = Field(..., description="When the job was created")
    started_at: Optional[datetime] = Field(None, description="When the job execution started")
    completed_at: Optional[datetime] = Field(None, description="When the job was completed")
    progress: Optional[str] = Field(None, description="Current progress information")
    error: Optional[str] = Field(None, description="Error message if job failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "running",
                "created_at": "2024-01-01T12:00:00Z",
                "started_at": "2024-01-01T12:00:05Z",
                "completed_at": None,
                "progress": "Researcher agent is analyzing the topic",
                "error": None
            }
        }


class JobResultResponse(BaseModel):
    """Response model for job results."""
    job_id: str = Field(..., description="Unique identifier for the job")
    status: JobStatus = Field(..., description="Final status of the job")
    created_at: datetime = Field(..., description="When the job was created")
    completed_at: Optional[datetime] = Field(None, description="When the job was completed")
    result: Optional[Dict[str, Any]] = Field(None, description="The crew execution result")
    error: Optional[str] = Field(None, description="Error message if job failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "job_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "completed",
                "created_at": "2024-01-01T12:00:00Z",
                "completed_at": "2024-01-01T12:05:30Z",
                "result": {
                    "research_findings": "...",
                    "final_report": "..."
                },
                "error": None
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response model."""
    error: str = Field(..., description="Error type or code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid input parameters",
                "details": {"field": "topic", "issue": "Field is required"}
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Service health status")
    timestamp: datetime = Field(..., description="Current timestamp")
    version: str = Field(..., description="API version")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2024-01-01T12:00:00Z",
                "version": "0.1.0"
            }
        }
