"""
FastAPI routes for CrewAI backend.
"""

import logging
from datetime import datetime
from typing import Dict

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import J<PERSON><PERSON>esponse

from .crew_manager import crew_manager
from .models import (
    CrewRunRequest, 
    CrewRunResponse, 
    JobStatusResponse, 
    JobResultResponse,
    ErrorResponse,
    HealthResponse,
    JobStatus
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version="0.1.0"
    )


@router.post("/crew/run", response_model=CrewRunResponse)
async def run_crew(request: CrewRunRequest):
    """Start a new crew execution."""
    try:
        # Prepare inputs for the crew
        inputs = {
            "topic": request.topic,
            "current_year": request.current_year or str(datetime.now().year)
        }
        
        # Start the crew job
        job_id = await crew_manager.start_crew_job(inputs)
        
        return CrewRunResponse(
            job_id=job_id,
            status=JobStatus.PENDING,
            message="Crew execution started successfully",
            created_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Error starting crew: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start crew execution: {str(e)}"
        )


@router.get("/crew/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get the status of a crew execution job."""
    job_status = crew_manager.get_job_status(job_id)
    
    if not job_status:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job {job_id} not found"
        )
    
    return job_status


@router.get("/crew/result/{job_id}", response_model=JobResultResponse)
async def get_job_result(job_id: str):
    """Get the result of a crew execution job."""
    job_result = crew_manager.get_job_result(job_id)
    
    if not job_result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job {job_id} not found"
        )
    
    if job_result.status == JobStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_202_ACCEPTED,
            detail=f"Job {job_id} is still pending"
        )
    
    if job_result.status == JobStatus.RUNNING:
        raise HTTPException(
            status_code=status.HTTP_202_ACCEPTED,
            detail=f"Job {job_id} is still running"
        )
    
    return job_result


@router.get("/crew/jobs", response_model=Dict[str, JobStatusResponse])
async def list_jobs():
    """List all crew execution jobs."""
    return crew_manager.list_jobs()


@router.delete("/crew/jobs/cleanup")
async def cleanup_old_jobs(max_age_hours: int = 24):
    """Clean up old completed jobs."""
    try:
        cleaned_count = crew_manager.cleanup_old_jobs(max_age_hours)
        return {
            "message": f"Cleaned up {cleaned_count} old jobs",
            "cleaned_count": cleaned_count
        }
    except Exception as e:
        logger.error(f"Error cleaning up jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup jobs: {str(e)}"
        )


# Note: Exception handlers are defined in main.py on the FastAPI app instance
