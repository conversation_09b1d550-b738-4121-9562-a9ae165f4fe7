"""
Vocabulary Manager Tool for Japanese language learning.
"""

from crewai.tools import BaseTool
from typing import Type, Dict, List, Any
from pydantic import BaseModel, Field
import json
import random


class VocabularyManagerInput(BaseModel):
    """Input schema for Vocabulary Manager Tool."""
    action: str = Field(..., description="Action to perform: 'generate_list', 'get_by_topic', 'get_by_level', or 'create_exercises'")
    jlpt_level: str = Field(default="N5", description="JLPT level (N5, N4, N3, N2, N1)")
    topic: str = Field(default="general", description="Topic category (e.g., 'food', 'family', 'business', 'travel')")
    word_count: int = Field(default=10, description="Number of vocabulary items to return")
    include_examples: bool = Field(default=True, description="Include example sentences")


class VocabularyManagerTool(BaseTool):
    name: str = "Japanese Vocabulary Manager"
    description: str = (
        "Manage Japanese vocabulary for language learning. Generate vocabulary lists by JLPT level "
        "and topic, create practice exercises, and provide detailed word information including "
        "readings, meanings, and example sentences. Essential for lesson content creation and "
        "vocabulary practice."
    )
    args_schema: Type[BaseModel] = VocabularyManagerInput

    def _run(self, action: str, jlpt_level: str, topic: str, word_count: int, include_examples: bool) -> str:
        """Manage Japanese vocabulary based on the specified action."""
        
        # Sample vocabulary database organized by level and topic
        vocabulary_db = {
            "N5": {
                "greetings": [
                    {"word": "こんにちは", "reading": "こんにちは", "meaning": "hello, good afternoon", 
                     "example": "こんにちは、元気ですか。", "translation": "Hello, how are you?"},
                    {"word": "おはよう", "reading": "おはよう", "meaning": "good morning (casual)", 
                     "example": "おはよう、今日はいい天気ですね。", "translation": "Good morning, it's nice weather today."},
                    {"word": "さようなら", "reading": "さようなら", "meaning": "goodbye", 
                     "example": "さようなら、また明日。", "translation": "Goodbye, see you tomorrow."}
                ],
                "family": [
                    {"word": "家族", "reading": "かぞく", "meaning": "family", 
                     "example": "私の家族は四人です。", "translation": "My family has four people."},
                    {"word": "父", "reading": "ちち", "meaning": "father (humble)", 
                     "example": "父は会社員です。", "translation": "My father is a company employee."},
                    {"word": "母", "reading": "はは", "meaning": "mother (humble)", 
                     "example": "母は料理が上手です。", "translation": "My mother is good at cooking."}
                ],
                "food": [
                    {"word": "食べ物", "reading": "たべもの", "meaning": "food", 
                     "example": "日本の食べ物が好きです。", "translation": "I like Japanese food."},
                    {"word": "ご飯", "reading": "ごはん", "meaning": "rice, meal", 
                     "example": "朝ご飯を食べました。", "translation": "I ate breakfast."},
                    {"word": "水", "reading": "みず", "meaning": "water", 
                     "example": "水を飲みます。", "translation": "I drink water."}
                ]
            },
            "N4": {
                "weather": [
                    {"word": "天気", "reading": "てんき", "meaning": "weather", 
                     "example": "今日の天気はどうですか。", "translation": "How is today's weather?"},
                    {"word": "雨", "reading": "あめ", "meaning": "rain", 
                     "example": "明日は雨が降るでしょう。", "translation": "It will probably rain tomorrow."},
                    {"word": "雪", "reading": "ゆき", "meaning": "snow", 
                     "example": "冬には雪がたくさん降ります。", "translation": "A lot of snow falls in winter."}
                ],
                "travel": [
                    {"word": "旅行", "reading": "りょこう", "meaning": "travel, trip", 
                     "example": "来月、京都に旅行します。", "translation": "I will travel to Kyoto next month."},
                    {"word": "電車", "reading": "でんしゃ", "meaning": "train", 
                     "example": "電車で学校に行きます。", "translation": "I go to school by train."},
                    {"word": "空港", "reading": "くうこう", "meaning": "airport", 
                     "example": "空港で友達を待っています。", "translation": "I'm waiting for my friend at the airport."}
                ]
            },
            "N3": {
                "business": [
                    {"word": "会議", "reading": "かいぎ", "meaning": "meeting, conference", 
                     "example": "午後に重要な会議があります。", "translation": "There's an important meeting this afternoon."},
                    {"word": "経験", "reading": "けいけん", "meaning": "experience", 
                     "example": "この仕事には経験が必要です。", "translation": "Experience is necessary for this job."},
                    {"word": "責任", "reading": "せきにん", "meaning": "responsibility", 
                     "example": "プロジェクトの責任者です。", "translation": "I'm the person responsible for the project."}
                ],
                "education": [
                    {"word": "教育", "reading": "きょういく", "meaning": "education", 
                     "example": "教育は社会の基盤です。", "translation": "Education is the foundation of society."},
                    {"word": "研究", "reading": "けんきゅう", "meaning": "research", 
                     "example": "大学で日本語を研究しています。", "translation": "I'm researching Japanese at university."},
                    {"word": "知識", "reading": "ちしき", "meaning": "knowledge", 
                     "example": "専門的な知識が必要です。", "translation": "Specialized knowledge is necessary."}
                ]
            }
        }
        
        if action == "generate_list":
            return self._generate_vocabulary_list(vocabulary_db, jlpt_level, topic, word_count, include_examples)
        elif action == "get_by_topic":
            return self._get_vocabulary_by_topic(vocabulary_db, topic, jlpt_level, word_count, include_examples)
        elif action == "get_by_level":
            return self._get_vocabulary_by_level(vocabulary_db, jlpt_level, word_count, include_examples)
        elif action == "create_exercises":
            return self._create_vocabulary_exercises(vocabulary_db, jlpt_level, topic, word_count)
        else:
            return json.dumps({"error": f"Unknown action: {action}"})

    def _generate_vocabulary_list(self, vocab_db: Dict, level: str, topic: str, count: int, examples: bool) -> str:
        """Generate a vocabulary list for the specified level and topic."""
        
        if level not in vocab_db:
            return json.dumps({"error": f"Level {level} not found in vocabulary database"})
        
        level_vocab = vocab_db[level]
        
        # If specific topic requested, use that; otherwise combine all topics
        if topic != "general" and topic in level_vocab:
            vocab_items = level_vocab[topic]
        else:
            vocab_items = []
            for topic_items in level_vocab.values():
                vocab_items.extend(topic_items)
        
        # Select random vocabulary items
        selected_items = random.sample(vocab_items, min(count, len(vocab_items)))
        
        # Format vocabulary list
        vocabulary_list = {
            "level": level,
            "topic": topic,
            "total_items": len(selected_items),
            "vocabulary": []
        }
        
        for item in selected_items:
            vocab_entry = {
                "word": item["word"],
                "reading": item["reading"],
                "meaning": item["meaning"],
                "level": level
            }
            
            if examples:
                vocab_entry["example_sentence"] = item.get("example", "")
                vocab_entry["example_translation"] = item.get("translation", "")
            
            vocabulary_list["vocabulary"].append(vocab_entry)
        
        return json.dumps(vocabulary_list, ensure_ascii=False, indent=2)

    def _get_vocabulary_by_topic(self, vocab_db: Dict, topic: str, level: str, count: int, examples: bool) -> str:
        """Get vocabulary items for a specific topic across levels."""
        
        topic_vocabulary = []
        
        for lvl, topics in vocab_db.items():
            if topic in topics:
                for item in topics[topic]:
                    vocab_item = item.copy()
                    vocab_item["level"] = lvl
                    topic_vocabulary.append(vocab_item)
        
        # Select items, prioritizing the specified level
        if level != "all":
            level_items = [item for item in topic_vocabulary if item["level"] == level]
            other_items = [item for item in topic_vocabulary if item["level"] != level]
            selected_items = level_items[:count] + other_items[:max(0, count - len(level_items))]
        else:
            selected_items = random.sample(topic_vocabulary, min(count, len(topic_vocabulary)))
        
        result = {
            "topic": topic,
            "requested_level": level,
            "total_items": len(selected_items),
            "vocabulary": selected_items[:count]
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)

    def _get_vocabulary_by_level(self, vocab_db: Dict, level: str, count: int, examples: bool) -> str:
        """Get vocabulary items for a specific JLPT level across all topics."""
        
        if level not in vocab_db:
            return json.dumps({"error": f"Level {level} not found"})
        
        all_vocab = []
        for topic, items in vocab_db[level].items():
            for item in items:
                vocab_item = item.copy()
                vocab_item["topic"] = topic
                vocab_item["level"] = level
                all_vocab.append(vocab_item)
        
        selected_items = random.sample(all_vocab, min(count, len(all_vocab)))
        
        result = {
            "level": level,
            "total_items": len(selected_items),
            "vocabulary": selected_items
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)

    def _create_vocabulary_exercises(self, vocab_db: Dict, level: str, topic: str, count: int) -> str:
        """Create vocabulary practice exercises."""
        
        # Get vocabulary items
        vocab_items = []
        if level in vocab_db:
            if topic != "general" and topic in vocab_db[level]:
                vocab_items = vocab_db[level][topic]
            else:
                for topic_items in vocab_db[level].values():
                    vocab_items.extend(topic_items)
        
        if not vocab_items:
            return json.dumps({"error": "No vocabulary items found for the specified criteria"})
        
        selected_items = random.sample(vocab_items, min(count, len(vocab_items)))
        
        exercises = {
            "level": level,
            "topic": topic,
            "exercise_count": len(selected_items),
            "exercises": []
        }
        
        for i, item in enumerate(selected_items):
            # Create different types of exercises
            exercise_types = ["meaning_choice", "reading_choice", "translation"]
            exercise_type = random.choice(exercise_types)
            
            if exercise_type == "meaning_choice":
                # Multiple choice for meaning
                correct_meaning = item["meaning"]
                wrong_options = ["incorrect option 1", "incorrect option 2", "incorrect option 3"]
                options = [correct_meaning] + wrong_options
                random.shuffle(options)
                
                exercise = {
                    "exercise_id": f"vocab_ex_{i+1}",
                    "type": "multiple_choice",
                    "question": f"What does '{item['word']}' mean?",
                    "options": options,
                    "correct_answer": correct_meaning,
                    "word": item["word"],
                    "reading": item["reading"]
                }
            
            elif exercise_type == "reading_choice":
                # Multiple choice for reading
                correct_reading = item["reading"]
                wrong_readings = ["よみかた1", "よみかた2", "よみかた3"]
                options = [correct_reading] + wrong_readings
                random.shuffle(options)
                
                exercise = {
                    "exercise_id": f"vocab_ex_{i+1}",
                    "type": "multiple_choice",
                    "question": f"How do you read '{item['word']}'?",
                    "options": options,
                    "correct_answer": correct_reading,
                    "word": item["word"],
                    "meaning": item["meaning"]
                }
            
            else:  # translation
                exercise = {
                    "exercise_id": f"vocab_ex_{i+1}",
                    "type": "translation",
                    "question": f"Translate: {item.get('example', item['word'])}",
                    "correct_answer": item.get("translation", item["meaning"]),
                    "word": item["word"],
                    "reading": item["reading"],
                    "meaning": item["meaning"]
                }
            
            exercises["exercises"].append(exercise)
        
        return json.dumps(exercises, ensure_ascii=False, indent=2)
