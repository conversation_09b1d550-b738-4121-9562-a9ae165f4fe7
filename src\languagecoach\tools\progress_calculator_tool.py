"""
Progress Calculator Tool for tracking Japanese language learning progress.
"""

from crewai.tools import BaseTool
from typing import Type, Dict, List, Any, Optional
from pydantic import BaseModel, Field
import json
from datetime import datetime, timedelta
import statistics


class ProgressCalculatorInput(BaseModel):
    """Input schema for Progress Calculator Tool."""
    action: str = Field(..., description="Action: 'calculate_progress', 'analyze_performance', 'predict_timeline', or 'generate_report'")
    student_id: str = Field(..., description="Student identifier")
    progress_data: str = Field(..., description="JSON string of student progress data")
    target_level: str = Field(default="N3", description="Target JLPT level")
    analysis_period_days: int = Field(default=30, description="Period for analysis in days")


class ProgressCalculatorTool(BaseTool):
    name: str = "Japanese Learning Progress Calculator"
    description: str = (
        "Calculate and analyze Japanese language learning progress. Track lesson completion, "
        "performance metrics, learning velocity, and predict timelines to reach target levels. "
        "Provides detailed analytics for optimizing learning paths and identifying areas for improvement."
    )
    args_schema: Type[BaseModel] = ProgressCalculatorInput

    def _run(self, action: str, student_id: str, progress_data: str, target_level: str, analysis_period_days: int) -> str:
        """Calculate progress metrics based on the specified action."""
        
        try:
            data = json.loads(progress_data)
        except json.JSONDecodeError:
            return json.dumps({"error": "Invalid JSON format in progress data"})
        
        if action == "calculate_progress":
            return self._calculate_overall_progress(student_id, data, target_level)
        elif action == "analyze_performance":
            return self._analyze_performance_trends(student_id, data, analysis_period_days)
        elif action == "predict_timeline":
            return self._predict_learning_timeline(student_id, data, target_level)
        elif action == "generate_report":
            return self._generate_comprehensive_report(student_id, data, target_level, analysis_period_days)
        else:
            return json.dumps({"error": f"Unknown action: {action}"})

    def _calculate_overall_progress(self, student_id: str, data: Dict, target_level: str) -> str:
        """Calculate overall learning progress metrics."""
        
        # Extract progress information
        lessons_completed = data.get("lessons_completed", [])
        total_lessons = data.get("total_lessons_in_roadmap", 100)
        current_level = data.get("current_level", "N5")
        assessment_scores = data.get("assessment_scores", [])
        study_sessions = data.get("study_sessions", [])
        
        # Calculate completion percentage
        completion_percentage = (len(lessons_completed) / total_lessons) * 100 if total_lessons > 0 else 0
        
        # Calculate average scores
        if assessment_scores:
            avg_score = statistics.mean([score.get("score", 0) for score in assessment_scores])
            recent_scores = assessment_scores[-5:] if len(assessment_scores) >= 5 else assessment_scores
            recent_avg = statistics.mean([score.get("score", 0) for score in recent_scores])
        else:
            avg_score = 0
            recent_avg = 0
        
        # Calculate study consistency
        if study_sessions:
            total_study_time = sum([session.get("duration_minutes", 0) for session in study_sessions])
            study_days = len(set([session.get("date", "")[:10] for session in study_sessions]))
            avg_session_length = total_study_time / len(study_sessions) if study_sessions else 0
        else:
            total_study_time = 0
            study_days = 0
            avg_session_length = 0
        
        # Calculate learning velocity (lessons per week)
        if study_sessions and len(study_sessions) > 1:
            first_session = min(study_sessions, key=lambda x: x.get("date", ""))
            last_session = max(study_sessions, key=lambda x: x.get("date", ""))
            
            try:
                start_date = datetime.fromisoformat(first_session.get("date", "").replace("Z", "+00:00"))
                end_date = datetime.fromisoformat(last_session.get("date", "").replace("Z", "+00:00"))
                weeks_studied = max(1, (end_date - start_date).days / 7)
                lessons_per_week = len(lessons_completed) / weeks_studied
            except:
                lessons_per_week = 0
        else:
            lessons_per_week = 0
        
        # Level progression mapping
        level_order = ["N5", "N4", "N3", "N2", "N1"]
        current_index = level_order.index(current_level) if current_level in level_order else 0
        target_index = level_order.index(target_level) if target_level in level_order else 2
        
        levels_to_progress = max(0, target_index - current_index)
        level_progress_percentage = (current_index / target_index) * 100 if target_index > 0 else 100
        
        progress_result = {
            "student_id": student_id,
            "current_level": current_level,
            "target_level": target_level,
            "overall_metrics": {
                "completion_percentage": round(completion_percentage, 1),
                "lessons_completed": len(lessons_completed),
                "total_lessons": total_lessons,
                "level_progress_percentage": round(level_progress_percentage, 1),
                "levels_remaining": levels_to_progress
            },
            "performance_metrics": {
                "average_score": round(avg_score, 1),
                "recent_average_score": round(recent_avg, 1),
                "score_trend": "improving" if recent_avg > avg_score else "stable" if recent_avg == avg_score else "declining",
                "total_assessments": len(assessment_scores)
            },
            "study_metrics": {
                "total_study_time_hours": round(total_study_time / 60, 1),
                "study_days": study_days,
                "average_session_length_minutes": round(avg_session_length, 1),
                "lessons_per_week": round(lessons_per_week, 2),
                "consistency_score": min(100, (study_days / 30) * 100)  # Based on last 30 days
            },
            "calculated_at": datetime.now().isoformat()
        }
        
        return json.dumps(progress_result, ensure_ascii=False, indent=2)

    def _analyze_performance_trends(self, student_id: str, data: Dict, period_days: int) -> str:
        """Analyze performance trends over the specified period."""
        
        assessment_scores = data.get("assessment_scores", [])
        lesson_scores = data.get("lesson_scores", [])
        study_sessions = data.get("study_sessions", [])
        
        # Filter data for the analysis period
        cutoff_date = datetime.now() - timedelta(days=period_days)
        
        recent_assessments = []
        recent_lessons = []
        recent_sessions = []
        
        for assessment in assessment_scores:
            try:
                assessment_date = datetime.fromisoformat(assessment.get("date", "").replace("Z", "+00:00"))
                if assessment_date >= cutoff_date:
                    recent_assessments.append(assessment)
            except:
                continue
        
        for lesson in lesson_scores:
            try:
                lesson_date = datetime.fromisoformat(lesson.get("completed_at", "").replace("Z", "+00:00"))
                if lesson_date >= cutoff_date:
                    recent_lessons.append(lesson)
            except:
                continue
        
        for session in study_sessions:
            try:
                session_date = datetime.fromisoformat(session.get("date", "").replace("Z", "+00:00"))
                if session_date >= cutoff_date:
                    recent_sessions.append(session)
            except:
                continue
        
        # Analyze trends
        performance_trends = {
            "analysis_period_days": period_days,
            "assessment_trend": self._calculate_trend([a.get("score", 0) for a in recent_assessments]),
            "lesson_performance_trend": self._calculate_trend([l.get("score", 0) for l in recent_lessons]),
            "study_frequency_trend": self._analyze_study_frequency(recent_sessions),
            "skill_area_analysis": self._analyze_skill_areas(recent_assessments, recent_lessons),
            "recommendations": []
        }
        
        # Generate recommendations based on trends
        if performance_trends["assessment_trend"]["direction"] == "declining":
            performance_trends["recommendations"].append("Consider reviewing fundamental concepts")
            performance_trends["recommendations"].append("Increase practice frequency for weak areas")
        
        if performance_trends["study_frequency_trend"]["consistency_score"] < 70:
            performance_trends["recommendations"].append("Establish a more consistent study schedule")
            performance_trends["recommendations"].append("Set daily study reminders")
        
        if not performance_trends["recommendations"]:
            performance_trends["recommendations"].append("Continue current study approach")
            performance_trends["recommendations"].append("Consider increasing difficulty level")
        
        return json.dumps(performance_trends, ensure_ascii=False, indent=2)

    def _predict_learning_timeline(self, student_id: str, data: Dict, target_level: str) -> str:
        """Predict timeline to reach target level based on current progress."""
        
        current_level = data.get("current_level", "N5")
        lessons_completed = len(data.get("lessons_completed", []))
        study_sessions = data.get("study_sessions", [])
        
        # Calculate current learning velocity
        if len(study_sessions) >= 2:
            try:
                first_session = min(study_sessions, key=lambda x: x.get("date", ""))
                last_session = max(study_sessions, key=lambda x: x.get("date", ""))
                
                start_date = datetime.fromisoformat(first_session.get("date", "").replace("Z", "+00:00"))
                end_date = datetime.fromisoformat(last_session.get("date", "").replace("Z", "+00:00"))
                
                days_studied = (end_date - start_date).days
                lessons_per_day = lessons_completed / max(1, days_studied)
            except:
                lessons_per_day = 0.1  # Default conservative estimate
        else:
            lessons_per_day = 0.1
        
        # Estimate lessons needed per level
        lessons_per_level = {
            "N5_to_N4": 50,
            "N4_to_N3": 75,
            "N3_to_N2": 100,
            "N2_to_N1": 150
        }
        
        level_order = ["N5", "N4", "N3", "N2", "N1"]
        current_index = level_order.index(current_level) if current_level in level_order else 0
        target_index = level_order.index(target_level) if target_level in level_order else 2
        
        total_lessons_needed = 0
        for i in range(current_index, target_index):
            level_transition = f"{level_order[i]}_to_{level_order[i+1]}"
            total_lessons_needed += lessons_per_level.get(level_transition, 75)
        
        # Calculate timeline
        if lessons_per_day > 0:
            estimated_days = total_lessons_needed / lessons_per_day
            estimated_weeks = estimated_days / 7
            estimated_months = estimated_weeks / 4.33
        else:
            estimated_days = estimated_weeks = estimated_months = 0
        
        # Adjust based on study consistency
        study_days = len(set([session.get("date", "")[:10] for session in study_sessions]))
        consistency_factor = min(1.0, study_days / 30)  # Based on last 30 days
        
        if consistency_factor < 0.5:
            # Low consistency, increase timeline
            estimated_months *= 1.5
        elif consistency_factor > 0.8:
            # High consistency, slightly reduce timeline
            estimated_months *= 0.9
        
        timeline_prediction = {
            "student_id": student_id,
            "current_level": current_level,
            "target_level": target_level,
            "prediction": {
                "estimated_months": round(estimated_months, 1),
                "estimated_weeks": round(estimated_weeks, 1),
                "estimated_days": round(estimated_days, 0),
                "lessons_remaining": total_lessons_needed,
                "current_pace_lessons_per_day": round(lessons_per_day, 3)
            },
            "factors": {
                "consistency_score": round(consistency_factor * 100, 1),
                "current_performance": "good" if lessons_per_day > 0.2 else "moderate" if lessons_per_day > 0.1 else "slow",
                "adjustment_applied": "timeline_extended" if consistency_factor < 0.5 else "timeline_optimized" if consistency_factor > 0.8 else "no_adjustment"
            },
            "recommendations": [
                f"Maintain current study pace of {round(lessons_per_day * 7, 1)} lessons per week",
                "Take regular progress assessments to track improvement",
                "Focus on consistent daily study habits"
            ]
        }
        
        return json.dumps(timeline_prediction, ensure_ascii=False, indent=2)

    def _generate_comprehensive_report(self, student_id: str, data: Dict, target_level: str, period_days: int) -> str:
        """Generate a comprehensive progress report combining all metrics."""
        
        # Get individual analyses
        progress_result = json.loads(self._calculate_overall_progress(student_id, data, target_level))
        performance_trends = json.loads(self._analyze_performance_trends(student_id, data, period_days))
        timeline_prediction = json.loads(self._predict_learning_timeline(student_id, data, target_level))
        
        # Combine into comprehensive report
        comprehensive_report = {
            "student_id": student_id,
            "report_generated_at": datetime.now().isoformat(),
            "summary": {
                "current_level": progress_result["current_level"],
                "target_level": target_level,
                "overall_progress_percentage": progress_result["overall_metrics"]["completion_percentage"],
                "estimated_time_to_target": f"{timeline_prediction['prediction']['estimated_months']} months",
                "performance_status": "excellent" if progress_result["performance_metrics"]["recent_average_score"] >= 85 
                                   else "good" if progress_result["performance_metrics"]["recent_average_score"] >= 70
                                   else "needs_improvement"
            },
            "detailed_metrics": progress_result,
            "performance_analysis": performance_trends,
            "timeline_prediction": timeline_prediction,
            "key_insights": [
                f"Completed {progress_result['overall_metrics']['lessons_completed']} out of {progress_result['overall_metrics']['total_lessons']} lessons",
                f"Current average score: {progress_result['performance_metrics']['recent_average_score']}%",
                f"Study consistency: {progress_result['study_metrics']['consistency_score']}%",
                f"Learning pace: {progress_result['study_metrics']['lessons_per_week']} lessons per week"
            ],
            "action_items": performance_trends["recommendations"] + timeline_prediction["recommendations"]
        }
        
        return json.dumps(comprehensive_report, ensure_ascii=False, indent=2)

    def _calculate_trend(self, values: List[float]) -> Dict:
        """Calculate trend direction and strength for a list of values."""
        if len(values) < 2:
            return {"direction": "insufficient_data", "strength": 0, "change": 0}
        
        # Simple linear trend calculation
        x = list(range(len(values)))
        n = len(values)
        
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)
        
        if n * sum_x2 - sum_x * sum_x == 0:
            slope = 0
        else:
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        direction = "improving" if slope > 0.1 else "declining" if slope < -0.1 else "stable"
        strength = abs(slope)
        change = values[-1] - values[0] if values else 0
        
        return {
            "direction": direction,
            "strength": round(strength, 3),
            "change": round(change, 2),
            "data_points": len(values)
        }

    def _analyze_study_frequency(self, sessions: List[Dict]) -> Dict:
        """Analyze study frequency and consistency."""
        if not sessions:
            return {"consistency_score": 0, "frequency": "no_data"}
        
        # Group sessions by date
        study_dates = set()
        for session in sessions:
            date_str = session.get("date", "")[:10]  # Get date part only
            if date_str:
                study_dates.add(date_str)
        
        unique_study_days = len(study_dates)
        total_sessions = len(sessions)
        
        # Calculate consistency score based on regularity
        if unique_study_days > 0:
            sessions_per_day = total_sessions / unique_study_days
            consistency_score = min(100, (unique_study_days / 30) * 100)  # Based on 30-day period
        else:
            sessions_per_day = 0
            consistency_score = 0
        
        frequency = "high" if unique_study_days >= 20 else "medium" if unique_study_days >= 10 else "low"
        
        return {
            "consistency_score": round(consistency_score, 1),
            "frequency": frequency,
            "unique_study_days": unique_study_days,
            "total_sessions": total_sessions,
            "sessions_per_day": round(sessions_per_day, 2)
        }

    def _analyze_skill_areas(self, assessments: List[Dict], lessons: List[Dict]) -> Dict:
        """Analyze performance by skill area."""
        skill_performance = {}
        
        # Analyze assessment performance by skill
        for assessment in assessments:
            skill_scores = assessment.get("skill_scores", {})
            for skill, score in skill_scores.items():
                if skill not in skill_performance:
                    skill_performance[skill] = []
                skill_performance[skill].append(score)
        
        # Calculate averages and trends for each skill
        skill_analysis = {}
        for skill, scores in skill_performance.items():
            if scores:
                avg_score = statistics.mean(scores)
                trend = self._calculate_trend(scores)
                skill_analysis[skill] = {
                    "average_score": round(avg_score, 1),
                    "trend": trend["direction"],
                    "performance_level": "strong" if avg_score >= 80 else "moderate" if avg_score >= 60 else "weak"
                }
        
        return skill_analysis
