#!/usr/bin/env python3
"""
Test script for the CrewAI FastAPI backend.
"""

import json
import time
import requests


def test_api():
    """Test the CrewAI FastAPI API endpoints."""
    base_url = "http://127.0.0.1:8000/api/v1"
    
    print("🚀 Testing CrewAI FastAPI Backend")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        return
    
    # Test crew run endpoint
    print("\n2. Testing crew run endpoint...")
    try:
        payload = {
            "topic": "Artificial Intelligence and Machine Learning",
            "current_year": "2024"
        }
        
        response = requests.post(f"{base_url}/crew/run", json=payload)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200:
            job_id = result["job_id"]
            print(f"\n✅ Job started successfully with ID: {job_id}")
            
            # Test job status endpoint
            print("\n3. Testing job status endpoint...")
            for i in range(5):
                time.sleep(2)
                status_response = requests.get(f"{base_url}/crew/status/{job_id}")
                print(f"Status check {i+1}: {status_response.status_code}")
                status_result = status_response.json()
                print(f"Job Status: {status_result['status']}")
                print(f"Progress: {status_result.get('progress', 'N/A')}")
                
                if status_result['status'] in ['completed', 'failed']:
                    break
            
            # Test job result endpoint
            print("\n4. Testing job result endpoint...")
            result_response = requests.get(f"{base_url}/crew/result/{job_id}")
            print(f"Status: {result_response.status_code}")
            if result_response.status_code == 200:
                result_data = result_response.json()
                if result_data['status'] == 'completed':
                    print(f"Job completed successfully!")
                    result_content = result_data.get('result')
                    if result_content:
                        print(f"Result keys: {list(result_content.keys())}")
                    else:
                        print("No result content available")
                elif result_data['status'] == 'failed':
                    print(f"Job failed: {result_data.get('error', 'Unknown error')}")
                else:
                    print(f"Job status: {result_data['status']}")
            else:
                print(f"Result: {json.dumps(result_response.json(), indent=2)}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Test list jobs endpoint
    print("\n5. Testing list jobs endpoint...")
    try:
        response = requests.get(f"{base_url}/crew/jobs")
        print(f"Status: {response.status_code}")
        jobs = response.json()
        print(f"Number of jobs: {len(jobs)}")
        for job_id, job_info in jobs.items():
            print(f"  Job {job_id}: {job_info['status']}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API testing completed!")


if __name__ == "__main__":
    test_api()
