"""
Japanese Language Assessment Tool for generating and evaluating language proficiency tests.
"""

from crewai.tools import BaseTool
from typing import Type, Dict, List, Any
from pydantic import BaseModel, Field
import json
import random


class JapaneseAssessmentInput(BaseModel):
    """Input schema for Japanese Assessment Tool."""
    assessment_type: str = Field(..., description="Type of assessment: 'level_placement', 'progress_check', or 'skill_specific'")
    target_levels: List[str] = Field(default=["N5", "N4", "N3"], description="JLPT levels to include in assessment")
    skill_areas: List[str] = Field(default=["vocabulary", "grammar", "kanji", "reading"], description="Skill areas to assess")
    question_count: int = Field(default=25, description="Total number of questions to generate")
    native_language: str = Field(default="English", description="Student's native language for context")


class JapaneseAssessmentTool(BaseTool):
    name: str = "Japanese Assessment Generator"
    description: str = (
        "Generate comprehensive Japanese language assessment questions across different JLPT levels "
        "and skill areas. Creates questions for vocabulary, grammar, kanji recognition, reading "
        "comprehension, and cultural context. Useful for determining student proficiency levels "
        "and tracking learning progress."
    )
    args_schema: Type[BaseModel] = JapaneseAssessmentInput

    def _run(self, assessment_type: str, target_levels: List[str], skill_areas: List[str], 
             question_count: int, native_language: str) -> str:
        """Generate Japanese language assessment questions."""
        
        # Sample question templates by level and skill area
        question_templates = {
            "N5": {
                "vocabulary": [
                    {
                        "question": "What does 'こんにちは' mean?",
                        "options": ["Good morning", "Good afternoon", "Good evening", "Good night"],
                        "correct": "Good afternoon",
                        "topic": "greetings"
                    },
                    {
                        "question": "How do you say 'water' in Japanese?",
                        "options": ["みず", "おちゃ", "コーヒー", "ジュース"],
                        "correct": "みず",
                        "topic": "drinks"
                    }
                ],
                "grammar": [
                    {
                        "question": "Complete: 私___学生です。",
                        "options": ["は", "を", "に", "で"],
                        "correct": "は",
                        "topic": "particles"
                    }
                ],
                "kanji": [
                    {
                        "question": "What is the reading of '人'?",
                        "options": ["ひと", "いぬ", "ねこ", "とり"],
                        "correct": "ひと",
                        "topic": "basic_kanji"
                    }
                ]
            },
            "N4": {
                "vocabulary": [
                    {
                        "question": "What does '天気' mean?",
                        "options": ["weather", "temperature", "season", "climate"],
                        "correct": "weather",
                        "topic": "weather"
                    }
                ],
                "grammar": [
                    {
                        "question": "Choose the correct form: 昨日映画___見ました。",
                        "options": ["を", "に", "で", "が"],
                        "correct": "を",
                        "topic": "object_particle"
                    }
                ]
            },
            "N3": {
                "vocabulary": [
                    {
                        "question": "What does '経験' mean?",
                        "options": ["experience", "experiment", "explanation", "expression"],
                        "correct": "experience",
                        "topic": "abstract_concepts"
                    }
                ],
                "grammar": [
                    {
                        "question": "Complete: 雨が降っている___、出かけません。",
                        "options": ["ので", "のに", "ために", "ながら"],
                        "correct": "ので",
                        "topic": "reason_clauses"
                    }
                ]
            }
        }
        
        # Generate assessment questions
        assessment_questions = []
        questions_per_skill = question_count // len(skill_areas)
        
        for skill in skill_areas:
            skill_questions = []
            for level in target_levels:
                if level in question_templates and skill in question_templates[level]:
                    level_questions = question_templates[level][skill]
                    skill_questions.extend(level_questions)
            
            # Select random questions for this skill area
            selected_questions = random.sample(
                skill_questions, 
                min(questions_per_skill, len(skill_questions))
            )
            
            for i, q in enumerate(selected_questions):
                question_data = {
                    "question_id": f"{skill}_{i+1}",
                    "skill_area": skill,
                    "level": random.choice(target_levels),
                    "question_type": "multiple_choice",
                    "question_text": q["question"],
                    "options": q["options"],
                    "correct_answer": q["correct"],
                    "topic": q["topic"],
                    "points": 1
                }
                assessment_questions.append(question_data)
        
        # Create assessment structure
        assessment = {
            "assessment_id": f"assessment_{random.randint(1000, 9999)}",
            "assessment_type": assessment_type,
            "target_levels": target_levels,
            "skill_areas": skill_areas,
            "total_questions": len(assessment_questions),
            "estimated_duration_minutes": len(assessment_questions) * 2,
            "questions": assessment_questions,
            "instructions": {
                "general": "Please answer all questions to the best of your ability. This assessment will help determine your current Japanese proficiency level.",
                "time_limit": "No strict time limit, but try to answer instinctively.",
                "scoring": "Each question is worth 1 point. Your overall score will determine your JLPT level."
            }
        }
        
        return json.dumps(assessment, ensure_ascii=False, indent=2)


class AssessmentEvaluationInput(BaseModel):
    """Input schema for Assessment Evaluation."""
    assessment_questions: str = Field(..., description="JSON string of assessment questions")
    student_responses: str = Field(..., description="JSON string of student responses")
    student_id: str = Field(..., description="Student identifier")


class AssessmentEvaluationTool(BaseTool):
    name: str = "Assessment Evaluator"
    description: str = (
        "Evaluate student responses to Japanese language assessment questions. "
        "Calculates scores, determines JLPT level, identifies strengths and weaknesses, "
        "and provides detailed feedback and recommendations."
    )
    args_schema: Type[BaseModel] = AssessmentEvaluationInput

    def _run(self, assessment_questions: str, student_responses: str, student_id: str) -> str:
        """Evaluate assessment responses and determine proficiency level."""
        
        try:
            questions = json.loads(assessment_questions)
            responses = json.loads(student_responses)
        except json.JSONDecodeError:
            return json.dumps({"error": "Invalid JSON format in input data"})
        
        # Evaluate responses
        total_questions = len(questions.get("questions", []))
        correct_answers = 0
        skill_scores = {}
        level_scores = {"N5": 0, "N4": 0, "N3": 0, "N2": 0, "N1": 0}
        level_totals = {"N5": 0, "N4": 0, "N3": 0, "N2": 0, "N1": 0}
        
        for question in questions.get("questions", []):
            question_id = question["question_id"]
            skill_area = question["skill_area"]
            level = question["level"]
            correct_answer = question["correct_answer"]
            
            # Initialize skill score tracking
            if skill_area not in skill_scores:
                skill_scores[skill_area] = {"correct": 0, "total": 0}
            
            skill_scores[skill_area]["total"] += 1
            level_totals[level] += 1
            
            # Check student response
            student_answer = responses.get(question_id, "")
            if student_answer.strip().lower() == correct_answer.strip().lower():
                correct_answers += 1
                skill_scores[skill_area]["correct"] += 1
                level_scores[level] += 1
        
        # Calculate overall score
        overall_score = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
        
        # Determine JLPT level based on performance
        level_percentages = {}
        for level in level_scores:
            if level_totals[level] > 0:
                level_percentages[level] = (level_scores[level] / level_totals[level]) * 100
            else:
                level_percentages[level] = 0
        
        # Determine proficiency level
        determined_level = "N5"  # Default
        if level_percentages.get("N1", 0) >= 70:
            determined_level = "N1"
        elif level_percentages.get("N2", 0) >= 70:
            determined_level = "N2"
        elif level_percentages.get("N3", 0) >= 70:
            determined_level = "N3"
        elif level_percentages.get("N4", 0) >= 70:
            determined_level = "N4"
        elif level_percentages.get("N5", 0) >= 60:
            determined_level = "N5"
        
        # Identify strengths and weaknesses
        strengths = []
        weaknesses = []
        
        for skill, scores in skill_scores.items():
            percentage = (scores["correct"] / scores["total"]) * 100 if scores["total"] > 0 else 0
            if percentage >= 75:
                strengths.append(f"{skill.title()} (Score: {percentage:.1f}%)")
            elif percentage < 50:
                weaknesses.append(f"{skill.title()} (Score: {percentage:.1f}%)")
        
        # Generate recommendations
        recommendations = []
        if "vocabulary" in [w.split()[0].lower() for w in weaknesses]:
            recommendations.append("Focus on expanding vocabulary through daily flashcard practice")
        if "grammar" in [w.split()[0].lower() for w in weaknesses]:
            recommendations.append("Review fundamental grammar patterns and practice sentence construction")
        if "kanji" in [w.split()[0].lower() for w in weaknesses]:
            recommendations.append("Practice kanji recognition and writing with spaced repetition")
        
        if not recommendations:
            recommendations.append("Continue current study approach and gradually increase difficulty level")
        
        # Create evaluation result
        evaluation_result = {
            "assessment_id": questions.get("assessment_id", "unknown"),
            "student_id": student_id,
            "determined_level": determined_level,
            "total_questions": total_questions,
            "correct_answers": correct_answers,
            "score_percentage": round(overall_score, 1),
            "level_scores": level_percentages,
            "skill_scores": {skill: round((scores["correct"] / scores["total"]) * 100, 1) 
                           for skill, scores in skill_scores.items() if scores["total"] > 0},
            "strengths": strengths if strengths else ["Consistent performance across skill areas"],
            "weaknesses": weaknesses if weaknesses else ["No significant weak areas identified"],
            "recommendations": recommendations,
            "next_steps": [
                f"Begin studying at {determined_level} level materials",
                "Take regular progress assessments to track improvement",
                "Focus on identified weak areas through targeted practice"
            ]
        }
        
        return json.dumps(evaluation_result, ensure_ascii=False, indent=2)
